# Email Content Parsing Implementation Plan

## Overview
Add a feature to prepare email contents for display in the interface by parsing and formatting the content to handle URLs, images, tables, and other elements that can break layout width.

## Current State
- Email content is displayed using `{$communication.content}` in templates
- Content is raw HTML without any processing
- No width limitations or formatting applied

## Requirements

### Content Parsing Rules
1. **URLs**: Convert URL-looking strings outside of `<a>` tags to clickable links with shortened labels
2. **Images**: Add `class='nz-communication__content--img'` to `<img>` tags for max-width: 800px
3. **Tables**: Wrap `<table>` elements in `<div class="nz-communication__content--table-wrapper">` for width control

### URL Shortening Logic
- Show protocol + host (if host ≤ 30 chars, otherwise truncate to 30)
- Show path from last slash to `?` (if ≤ 30 chars, otherwise truncate to 30)
- Show query parameters after `?` (if ≤ 30 chars, otherwise truncate to 30)
- Use ellipsis (`...`) for truncated parts

### Other Problematic Elements
Elements that can break layout width:
- `<pre>` tags (preserve whitespace, cause horizontal overflow)
- `<code>` blocks (similar to pre)
- `<iframe>`, `<embed>`, `<object>` (external content)
- `<video>`, `<audio>` (media elements)
- Long unbroken text strings
- Wide `<div>` elements with fixed widths

## Implementation Plan

### Files to Modify

#### 1. `_libs/modules/communications/viewers/communications.viewer.php`
**Changes:**
- Add new method `processContentForDisplay($content)` to parse and format email content
- Modify the data preparation section to add a new `processed_content` key to each communication item
- Process content after communications are retrieved from the factory

**New Method Functionality:**
- Parse URLs and convert to `<a>` tags with shortened labels
- Add CSS classes to `<img>` tags
- Wrap `<table>` elements in wrapper divs
- Handle other width-breaking elements (`<pre>`, `<code>`, `<iframe>`, etc.)
- Sanitize and validate HTML content

#### 2. `_libs/themes/Evolution/templates/_communication_list_item_email.html`
**Changes:**
- Line 84: Change `{$communication.content}` to `{$communication.processed_content}`
- Maintain existing template structure and styling

#### 3. `_libs/themes/Evolution/styles/communications.css`
**Changes:**
- Add new CSS classes for content formatting

### CSS Classes to Add

```css
/* Image width limitation */
.nz-communication__content--img {
    max-width: 800px;
    height: auto;
    display: block;
}

/* Table width control wrapper */
.nz-communication__content--table-wrapper {
    overflow-x: auto;
    max-width: 100%;
    margin: 0.5rem 0;
}

.nz-communication__content--table-wrapper table {
    max-width: 100%;
    width: auto;
}

/* Pre and code block formatting */
.nz-communication__content--pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-x: auto;
    max-width: 100%;
    background-color: var(--altbackground-color);
    padding: 0.5rem;
    border-radius: var(--border-radius);
}

.nz-communication__content--code {
    word-wrap: break-word;
    overflow-x: auto;
    max-width: 100%;
}

/* Media and embedded content */
.nz-communication__content--iframe,
.nz-communication__content--embed,
.nz-communication__content--object,
.nz-communication__content--video {
    max-width: 100%;
    height: auto;
}

/* Long text handling */
.nz-communication__content--long-text {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}
```

## Implementation Steps

### Step 1: Add CSS Classes
1. Open `_libs/themes/Evolution/styles/communications.css`
2. Add the new CSS classes at the end of the file
3. Follow existing CSS conventions and variable usage

### Step 2: Create Content Processing Method
1. Open `_libs/modules/communications/viewers/communications.viewer.php`
2. Add the `processContentForDisplay($content)` method
3. Implement URL parsing and link creation
4. Implement HTML element processing
5. Add proper error handling and validation

### Step 3: Integrate Processing into Viewer
1. Locate where communications data is prepared for templates
2. Add processing call for each communication item
3. Add `processed_content` key to communication array
4. Ensure backward compatibility by keeping original `content` key

### Step 4: Update Template
1. Open `_libs/themes/Evolution/templates/_communication_list_item_email.html`
2. Change line 84 from `{$communication.content}` to `{$communication.processed_content}`
3. Test template rendering

### Step 5: Testing
1. Test with various email content types:
   - Emails with URLs
   - Emails with images
   - Emails with tables
   - Emails with mixed content
2. Verify CSS styling works correctly
3. Check for any layout breaking issues
4. Validate HTML output

## Technical Considerations

### URL Detection Regex
Use a comprehensive regex pattern to detect URLs while avoiding those already in `<a>` tags:
- Match http/https URLs
- Match www. URLs
- Avoid URLs already wrapped in anchor tags
- Handle edge cases (URLs in attributes, etc.)

### HTML Processing
- Use PHP's DOMDocument for reliable HTML parsing
- Maintain existing HTML structure
- Preserve existing classes and attributes
- Add new classes without overriding existing ones

### Performance
- Process content only when needed
- Consider caching processed content if performance becomes an issue
- Minimize regex operations

### Security
- Sanitize URLs before creating links
- Validate HTML structure
- Prevent XSS attacks through proper escaping

## Fallback Strategy
- If processing fails, fall back to original content
- Log errors for debugging
- Ensure system remains functional even with processing errors

## Future Enhancements
- Add configuration options for URL shortening rules
- Support for additional media types
- Content preview/summary generation
- Rich text formatting improvements
